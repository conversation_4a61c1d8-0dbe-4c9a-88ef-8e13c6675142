import os
import torch
import lightning as L
from lightning.pytorch.callbacks import (
    ModelCheckpoint,
    EarlyStopping,
    LearningRateMonitor,
)
from lightning.pytorch.loggers import TensorBoardLogger

from src.model.lstm import LitLSTM, LitLSTMMultiStep, LitLSTMWithAttention, LitLSTMWithAttentionMultiStep
from src.data_loader import FlftrLoader
from src.config import TrainConfig
from datetime import datetime
import argparse


def train_lstm(args):
    """
    训练LSTM模型

    Args:
        args: 包含训练参数的命名空间
    """
    # 设定工作目录
    work_dir = f"data/train/{datetime.now().strftime('%Y%m%d')}"
    os.makedirs(work_dir, exist_ok=True)

    # 设置随机种子
    L.seed_everything(args.seed)

    # 加载数据
    start_time = datetime.strptime(args.start_time, "%Y-%m-%d")
    end_time = datetime.strptime(args.end_time, "%Y-%m-%d")

    data_config = {
        "debug": True,
        "freq": "1min",
        "min_data_length": 512,
        "split_ratio": [0.8, 0.1, 0.1],
        "normalize_method": "min-max",
        "seq_length": args.seq_length,
        "lookback": args.lookback,
        "horizon": args.horizon,
        "batch_size": args.batch_size,
    }

    data_module = FlftrLoader(
        start_time=start_time, end_time=end_time, work_dir=work_dir, config=TrainConfig(**data_config)
    )

    # 准备数据
    data_module.prepare_data()
    data_module.setup(stage="fit")

    # 获取一个批次以确定输入尺寸
    train_loader = data_module.train_dataloader()
    batch = next(iter(train_loader))
    x, y = batch
    input_size = x.shape[-1]
    output_size = y.shape[-1] if args.multi_step else y.shape[-1]

    print(f"输入特征维度: {input_size}")
    print(f"输出特征维度: {output_size}")
    print(f"数据批次形状 - 输入: {x.shape}, 输出: {y.shape}")

    # 创建模型
    if args.use_attention:
        # 使用注意力增强模型
        if args.multi_step:
            model = LitLSTMWithAttentionMultiStep(
                input_size=input_size,
                hidden_size=args.hidden_size,
                num_layers=args.num_layers,
                output_size=output_size,
                horizon=args.horizon,
                dropout=args.dropout,
                bidirectional=args.bidirectional,
                attention_type=args.attention_type,
                num_attention_heads=args.num_attention_heads,
                use_attention_residual=args.use_attention_residual,
                learning_rate=args.learning_rate,
                weight_decay=args.weight_decay,
            )
        else:
            model = LitLSTMWithAttention(
                input_size=input_size,
                hidden_size=args.hidden_size,
                num_layers=args.num_layers,
                output_size=output_size,
                dropout=args.dropout,
                bidirectional=args.bidirectional,
                attention_type=args.attention_type,
                num_attention_heads=args.num_attention_heads,
                use_attention_residual=args.use_attention_residual,
                learning_rate=args.learning_rate,
                weight_decay=args.weight_decay,
            )
    else:
        # 使用原始LSTM模型
        if args.multi_step:
            model = LitLSTMMultiStep(
                input_size=input_size,
                hidden_size=args.hidden_size,
                num_layers=args.num_layers,
                output_size=output_size,
                horizon=args.horizon,
                dropout=args.dropout,
                bidirectional=args.bidirectional,
                learning_rate=args.learning_rate,
                weight_decay=args.weight_decay,
            )
        else:
            model = LitLSTM(
                input_size=input_size,
                hidden_size=args.hidden_size,
                num_layers=args.num_layers,
                output_size=output_size,
                dropout=args.dropout,
                bidirectional=args.bidirectional,
                learning_rate=args.learning_rate,
                weight_decay=args.weight_decay,
            )

    # 设置回调
    checkpoint_callback = ModelCheckpoint(
        monitor="val_loss",
        filename="lstm-{epoch:02d}-{val_loss:.4f}",
        save_top_k=3,
        mode="min",
    )

    early_stopping = EarlyStopping(
        monitor="val_loss", patience=args.patience, mode="min"
    )

    lr_monitor = LearningRateMonitor(logging_interval="epoch")

    # 设置日志记录器
    logger = TensorBoardLogger("logs", name="lstm")

    # 创建训练器
    trainer = L.Trainer(
        max_epochs=args.epochs,
        callbacks=[checkpoint_callback, early_stopping, lr_monitor],
        logger=logger,
        accelerator=args.accelerator,
        devices=args.devices,
        gradient_clip_val=args.gradient_clip_val,
    )

    # 训练模型
    trainer.fit(
        model=model,
        train_dataloaders=data_module.train_dataloader(),
        val_dataloaders=data_module.val_dataloader(),
    )

    # 评估模型
    trainer.test(model=model, dataloaders=data_module.test_dataloader())

    # 保存模型
    save_path = os.path.join(work_dir, "lstm_model.ckpt")
    trainer.save_checkpoint(save_path)
    print(f"模型已保存到 {save_path}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="LSTM模型训练")

    # 数据参数
    parser.add_argument(
        "--start_time", type=str, default="2023-01-01", help="数据开始时间"
    )
    parser.add_argument(
        "--end_time", type=str, default="2023-12-31", help="数据结束时间"
    )
    parser.add_argument("--seq_length", type=int, default=256, help="序列长度")
    parser.add_argument("--lookback", type=int, default=255, help="历史窗口大小")
    parser.add_argument("--horizon", type=int, default=1, help="预测的时间步数")
    parser.add_argument("--batch_size", type=int, default=32, help="批次大小")

    # 模型参数
    parser.add_argument("--hidden_size", type=int, default=128, help="LSTM隐藏层大小")
    parser.add_argument("--num_layers", type=int, default=2, help="LSTM层数")
    parser.add_argument("--dropout", type=float, default=0.1, help="Dropout比率")
    parser.add_argument("--bidirectional", action="store_true", help="是否使用双向LSTM")
    parser.add_argument(
        "--multi_step", action="store_true", help="是否使用多步预测模型"
    )

    # 注意力机制参数
    parser.add_argument(
        "--use_attention", action="store_true", help="是否使用注意力机制"
    )
    parser.add_argument(
        "--attention_type",
        type=str,
        default="self",
        choices=["self", "multi_head"],
        help="注意力类型：self（自注意力）或multi_head（多头注意力）",
    )
    parser.add_argument(
        "--num_attention_heads", type=int, default=8, help="多头注意力的头数"
    )
    parser.add_argument(
        "--use_attention_residual",
        action="store_true",
        default=True,
        help="是否使用注意力残差连接",
    )

    # 训练参数
    parser.add_argument("--epochs", type=int, default=100, help="训练轮数")
    parser.add_argument("--learning_rate", type=float, default=1e-3, help="学习率")
    parser.add_argument("--weight_decay", type=float, default=1e-5, help="权重衰减系数")
    parser.add_argument("--patience", type=int, default=10, help="早停耐心值")
    parser.add_argument(
        "--gradient_clip_val", type=float, default=1.0, help="梯度裁剪值"
    )

    # 硬件参数
    parser.add_argument(
        "--accelerator",
        type=str,
        default="auto",
        choices=["cpu", "gpu", "tpu", "auto"],
        help="加速器类型",
    )
    parser.add_argument("--devices", type=int, default=1, help="设备数量")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")

    args = parser.parse_args()
    train_lstm(args)
