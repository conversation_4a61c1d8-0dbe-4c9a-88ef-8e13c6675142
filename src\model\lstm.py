import os
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import lightning as L
from torch.optim.lr_scheduler import ReduceLROnPlateau
from typing import Dict, List, Tuple, Optional, Union, Any
import math


class LSTMModel(nn.Module):
    """
    LSTM模型基础架构
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int,
        num_layers: int,
        output_size: int,
        dropout: float = 0.1,
        bidirectional: bool = False,
    ):
        """
        LSTM模型初始化

        Args:
            input_size: 输入特征的维度
            hidden_size: LSTM隐藏层的维度
            num_layers: LSTM层数
            output_size: 输出特征的维度
            dropout: dropout比率，默认为0.1
            bidirectional: 是否使用双向LSTM，默认为False
        """
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.output_size = output_size
        self.dropout = dropout
        self.bidirectional = bidirectional

        # 定义LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional,
        )

        # 定义输出层
        # 如果是双向LSTM，则隐藏层维度会翻倍
        lstm_output_size = hidden_size * 2 if bidirectional else hidden_size
        self.fc = nn.Sequential(
            nn.Linear(lstm_output_size, lstm_output_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(lstm_output_size // 2, output_size),
        )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """
        初始化模型权重
        """
        for name, param in self.lstm.named_parameters():
            if "weight" in name:
                nn.init.xavier_uniform_(param)
            elif "bias" in name:
                nn.init.zeros_(param)

        for name, param in self.fc.named_parameters():
            if "weight" in name:
                nn.init.xavier_uniform_(param)
            elif "bias" in name:
                nn.init.zeros_(param)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_size]

        Returns:
            输出张量，形状为 [batch_size, output_size]
        """
        # LSTM forward: x shape: [batch_size, seq_len, input_size]
        lstm_out, _ = self.lstm(x)

        # 我们只需要序列的最后一个时间步的输出
        last_output = lstm_out[:, -1, :]

        # 通过全连接层
        output = self.fc(last_output)

        return output


class LitLSTM(L.LightningModule):
    """
    PyTorch Lightning LSTM模型
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        num_layers: int = 2,
        output_size: int = 1,
        dropout: float = 0.1,
        bidirectional: bool = False,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-5,
    ):
        """
        初始化Lightning LSTM模型

        Args:
            input_size: 输入特征的维度
            hidden_size: LSTM隐藏层的维度，默认为128
            num_layers: LSTM的层数，默认为2
            output_size: 输出特征的维度，默认为1
            dropout: dropout比率，默认为0.1
            bidirectional: 是否使用双向LSTM，默认为False
            learning_rate: 学习率，默认为1e-3
            weight_decay: 权重衰减系数，默认为1e-5
        """
        super().__init__()
        self.save_hyperparameters()

        # 创建LSTM模型
        self.model = LSTMModel(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=output_size,
            dropout=dropout,
            bidirectional=bidirectional,
        )

        # 定义损失函数
        self.criterion = nn.MSELoss()

        # 记录训练参数
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_size]

        Returns:
            输出张量，形状为 [batch_size, output_size]
        """
        return self.model(x)

    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        训练步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含损失的字典
        """
        x, y = batch

        # 对于多步预测，我们只关心输入序列
        y_hat = self(x)

        # 这里假设y的最后一个维度是目标值
        # 在多步预测情况下，y的形状可能是[batch_size, horizon, output_size]
        # 在单步预测情况下，y的形状可能是[batch_size, output_size]
        if len(y.shape) == 3:
            # 多步预测：只取最后一个时间步
            y = y[:, -1, :]

        loss = self.criterion(y_hat, y)
        self.log("train_loss", loss, prog_bar=True, on_step=True, on_epoch=True)

        return {"loss": loss}

    def validation_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        验证步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含验证损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        if len(y.shape) == 3:
            y = y[:, -1, :]

        val_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("val_loss", val_loss, prog_bar=True)
        self.log("val_mae", mae, prog_bar=True)
        self.log("val_rmse", rmse, prog_bar=True)

        return {"val_loss": val_loss, "val_mae": mae, "val_rmse": rmse}

    def test_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        测试步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含测试损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        if len(y.shape) == 3:
            y = y[:, -1, :]

        test_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("test_loss", test_loss)
        self.log("test_mae", mae)
        self.log("test_rmse", rmse)

        return {"test_loss": test_loss, "test_mae": mae, "test_rmse": rmse}

    def predict_step(self, batch: torch.Tensor, batch_idx: int) -> torch.Tensor:
        """
        预测步骤

        Args:
            batch: 输入张量
            batch_idx: 批次索引

        Returns:
            预测结果
        """
        if isinstance(batch, tuple):
            x = batch[0]
        else:
            x = batch

        return self(x)

    def configure_optimizers(self) -> Dict[str, Any]:
        """
        配置优化器和学习率调度器

        Returns:
            包含优化器和学习率调度器的字典
        """
        optimizer = torch.optim.Adam(
            self.parameters(), lr=self.learning_rate, weight_decay=self.weight_decay
        )

        scheduler = {
            "scheduler": ReduceLROnPlateau(
                optimizer,
                mode="min",
                factor=0.5,
                patience=5,
                min_lr=1e-6,
                verbose="true",
            ),
            "monitor": "val_loss",
            "interval": "epoch",
            "frequency": 1,
        }

        return {"optimizer": optimizer, "lr_scheduler": scheduler}


# 多步预测版本的LSTM模型
class LitLSTMMultiStep(LitLSTM):
    """
    多步预测版本的PyTorch Lightning LSTM模型
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        num_layers: int = 2,
        output_size: int = 1,
        horizon: int = 1,
        dropout: float = 0.1,
        bidirectional: bool = False,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-5,
    ):
        """
        初始化多步预测版本的Lightning LSTM模型

        Args:
            input_size: 输入特征的维度
            hidden_size: LSTM隐藏层的维度，默认为128
            num_layers: LSTM的层数，默认为2
            output_size: 每个时间步的输出特征维度，默认为1
            horizon: 预测的时间步数，默认为1
            dropout: dropout比率，默认为0.1
            bidirectional: 是否使用双向LSTM，默认为False
            learning_rate: 学习率，默认为1e-3
            weight_decay: 权重衰减系数，默认为1e-5
        """
        # 调用父类初始化
        super().__init__(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=output_size
            * horizon,  # 总输出大小是每个时间步的特征数乘以时间步数
            dropout=dropout,
            bidirectional=bidirectional,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
        )

        # 保存预测的时间步数
        self.horizon = horizon
        self.output_dim = output_size

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播，并将输出重塑为多步预测格式

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_size]

        Returns:
            输出张量，形状为 [batch_size, horizon, output_dim]
        """
        # 获取模型原始输出
        output = super().forward(x)

        # 重塑为多步预测格式 [batch_size, horizon, output_dim]
        batch_size = x.size(0)
        output = output.view(batch_size, self.horizon, self.output_dim)

        return output

    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        训练步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含损失的字典
        """
        x, y = batch
        y_hat = self(x)

        # 计算多步预测的MSE
        loss = self.criterion(y_hat, y)
        self.log("train_loss", loss, prog_bar=True, on_step=True, on_epoch=True)

        return {"loss": loss}

    def validation_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        验证步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含验证损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        val_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("val_loss", val_loss, prog_bar=True)
        self.log("val_mae", mae, prog_bar=True)
        self.log("val_rmse", rmse, prog_bar=True)

        return {"val_loss": val_loss, "val_mae": mae, "val_rmse": rmse}

    def test_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        测试步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含测试损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        test_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("test_loss", test_loss)
        self.log("test_mae", mae)
        self.log("test_rmse", rmse)

        return {"test_loss": test_loss, "test_mae": mae, "test_rmse": rmse}


class MultiHeadAttention(nn.Module):
    """
    多头注意力机制层
    """

    def __init__(self, d_model: int, num_heads: int = 8, dropout: float = 0.1):
        """
        初始化多头注意力层

        Args:
            d_model: 模型的维度（输入特征维度）
            num_heads: 注意力头的数量，默认为8
            dropout: dropout比率，默认为0.1
        """
        super().__init__()
        assert d_model % num_heads == 0, "d_model必须能被num_heads整除"

        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads

        # 线性变换层
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)

        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_k)

    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播

        Args:
            query: 查询张量，形状为 [batch_size, seq_len, d_model]
            key: 键张量，形状为 [batch_size, seq_len, d_model]
            value: 值张量，形状为 [batch_size, seq_len, d_model]
            mask: 可选的掩码张量

        Returns:
            注意力输出，形状为 [batch_size, seq_len, d_model]
        """
        batch_size, seq_len, _ = query.size()

        # 线性变换并重塑为多头格式
        Q = self.w_q(query).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)

        # 计算注意力
        attention_output = self._scaled_dot_product_attention(Q, K, V, mask)

        # 重塑并通过输出线性层
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        output = self.w_o(attention_output)

        return output

    def _scaled_dot_product_attention(self, Q: torch.Tensor, K: torch.Tensor, V: torch.Tensor,
                                      mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        缩放点积注意力

        Args:
            Q: 查询张量，形状为 [batch_size, num_heads, seq_len, d_k]
            K: 键张量，形状为 [batch_size, num_heads, seq_len, d_k]
            V: 值张量，形状为 [batch_size, num_heads, seq_len, d_k]
            mask: 可选的掩码张量

        Returns:
            注意力输出，形状为 [batch_size, num_heads, seq_len, d_k]
        """
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale

        # 应用掩码（如果提供）
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)

        # 应用softmax
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        # 应用注意力权重到值
        output = torch.matmul(attention_weights, V)

        return output


class SelfAttention(nn.Module):
    """
    自注意力机制层（简化版本）
    """

    def __init__(self, d_model: int, dropout: float = 0.1):
        """
        初始化自注意力层

        Args:
            d_model: 模型的维度（输入特征维度）
            dropout: dropout比率，默认为0.1
        """
        super().__init__()
        self.d_model = d_model
        self.scale = math.sqrt(d_model)

        # 线性变换层
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)

        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, d_model]
            mask: 可选的掩码张量

        Returns:
            注意力输出，形状为 [batch_size, seq_len, d_model]
        """
        # 计算查询、键、值
        Q = self.w_q(x)
        K = self.w_k(x)
        V = self.w_v(x)

        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale

        # 应用掩码（如果提供）
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)

        # 应用softmax
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        # 应用注意力权重到值
        output = torch.matmul(attention_weights, V)

        return output


class LSTMWithAttentionModel(nn.Module):
    """
    集成注意力机制的LSTM模型基础架构
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int,
        num_layers: int,
        output_size: int,
        dropout: float = 0.1,
        bidirectional: bool = False,
        attention_type: str = "self",  # "self" 或 "multi_head"
        num_attention_heads: int = 8,
        use_attention_residual: bool = True,
    ):
        """
        LSTM + 注意力模型初始化

        Args:
            input_size: 输入特征的维度
            hidden_size: LSTM隐藏层的维度
            num_layers: LSTM层数
            output_size: 输出特征的维度
            dropout: dropout比率，默认为0.1
            bidirectional: 是否使用双向LSTM，默认为False
            attention_type: 注意力类型，"self"或"multi_head"，默认为"self"
            num_attention_heads: 多头注意力的头数，默认为8
            use_attention_residual: 是否使用残差连接，默认为True
        """
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.output_size = output_size
        self.dropout = dropout
        self.bidirectional = bidirectional
        self.attention_type = attention_type
        self.use_attention_residual = use_attention_residual

        # 定义LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional,
        )

        # 计算LSTM输出维度
        lstm_output_size = hidden_size * 2 if bidirectional else hidden_size

        # 定义注意力层
        if attention_type == "multi_head":
            self.attention = MultiHeadAttention(
                d_model=lstm_output_size,
                num_heads=num_attention_heads,
                dropout=dropout
            )
        elif attention_type == "self":
            self.attention = SelfAttention(
                d_model=lstm_output_size,
                dropout=dropout
            )
        else:
            raise ValueError(f"不支持的注意力类型: {attention_type}")

        # 层归一化
        self.layer_norm = nn.LayerNorm(lstm_output_size)

        # 定义输出层
        self.fc = nn.Sequential(
            nn.Linear(lstm_output_size, lstm_output_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(lstm_output_size // 2, output_size),
        )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """
        初始化模型权重
        """
        for name, param in self.lstm.named_parameters():
            if "weight" in name:
                nn.init.xavier_uniform_(param)
            elif "bias" in name:
                nn.init.zeros_(param)

        for name, param in self.fc.named_parameters():
            if "weight" in name:
                nn.init.xavier_uniform_(param)
            elif "bias" in name:
                nn.init.zeros_(param)

    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_size]
            mask: 可选的掩码张量

        Returns:
            输出张量，形状为 [batch_size, output_size]
        """
        # LSTM forward: x shape: [batch_size, seq_len, input_size]
        lstm_out, _ = self.lstm(x)

        # 应用注意力机制
        if self.attention_type == "multi_head":
            attention_out = self.attention(lstm_out, lstm_out, lstm_out, mask)
        else:  # self attention
            attention_out = self.attention(lstm_out, mask)

        # 残差连接和层归一化
        if self.use_attention_residual:
            attention_out = self.layer_norm(lstm_out + attention_out)
        else:
            attention_out = self.layer_norm(attention_out)

        # 全局平均池化或使用最后一个时间步
        # 这里我们使用注意力加权的全局平均池化
        pooled_output = self._global_attention_pooling(attention_out)

        # 通过全连接层
        output = self.fc(pooled_output)

        return output

    def _global_attention_pooling(self, x: torch.Tensor) -> torch.Tensor:
        """
        全局注意力池化

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, hidden_size]

        Returns:
            池化后的张量，形状为 [batch_size, hidden_size]
        """
        # 计算注意力权重
        attention_weights = torch.mean(x, dim=-1, keepdim=True)  # [batch_size, seq_len, 1]
        attention_weights = F.softmax(attention_weights, dim=1)

        # 加权平均
        pooled = torch.sum(x * attention_weights, dim=1)  # [batch_size, hidden_size]

        return pooled


class LitLSTMWithAttention(L.LightningModule):
    """
    集成注意力机制的PyTorch Lightning LSTM模型
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        num_layers: int = 2,
        output_size: int = 1,
        dropout: float = 0.1,
        bidirectional: bool = False,
        attention_type: str = "self",
        num_attention_heads: int = 8,
        use_attention_residual: bool = True,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-5,
    ):
        """
        初始化Lightning LSTM + 注意力模型

        Args:
            input_size: 输入特征的维度
            hidden_size: LSTM隐藏层的维度，默认为128
            num_layers: LSTM的层数，默认为2
            output_size: 输出特征的维度，默认为1
            dropout: dropout比率，默认为0.1
            bidirectional: 是否使用双向LSTM，默认为False
            attention_type: 注意力类型，"self"或"multi_head"，默认为"self"
            num_attention_heads: 多头注意力的头数，默认为8
            use_attention_residual: 是否使用残差连接，默认为True
            learning_rate: 学习率，默认为1e-3
            weight_decay: 权重衰减系数，默认为1e-5
        """
        super().__init__()
        self.save_hyperparameters()

        # 创建LSTM + 注意力模型
        self.model = LSTMWithAttentionModel(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=output_size,
            dropout=dropout,
            bidirectional=bidirectional,
            attention_type=attention_type,
            num_attention_heads=num_attention_heads,
            use_attention_residual=use_attention_residual,
        )

        # 定义损失函数
        self.criterion = nn.MSELoss()

        # 记录训练参数
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay

    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_size]
            mask: 可选的掩码张量

        Returns:
            输出张量，形状为 [batch_size, output_size]
        """
        return self.model(x, mask)

    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        训练步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含损失的字典
        """
        x, y = batch

        # 对于多步预测，我们只关心输入序列
        y_hat = self(x)

        # 这里假设y的最后一个维度是目标值
        # 在多步预测情况下，y的形状可能是[batch_size, horizon, output_size]
        # 在单步预测情况下，y的形状可能是[batch_size, output_size]
        if len(y.shape) == 3:
            # 多步预测：只取最后一个时间步
            y = y[:, -1, :]

        loss = self.criterion(y_hat, y)
        self.log("train_loss", loss, prog_bar=True, on_step=True, on_epoch=True)

        return {"loss": loss}

    def validation_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        验证步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含验证损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        if len(y.shape) == 3:
            y = y[:, -1, :]

        val_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("val_loss", val_loss, prog_bar=True)
        self.log("val_mae", mae, prog_bar=True)
        self.log("val_rmse", rmse, prog_bar=True)

        return {"val_loss": val_loss, "val_mae": mae, "val_rmse": rmse}

    def test_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        测试步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含测试损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        if len(y.shape) == 3:
            y = y[:, -1, :]

        test_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("test_loss", test_loss)
        self.log("test_mae", mae)
        self.log("test_rmse", rmse)

        return {"test_loss": test_loss, "test_mae": mae, "test_rmse": rmse}

    def predict_step(self, batch: torch.Tensor, batch_idx: int) -> torch.Tensor:
        """
        预测步骤

        Args:
            batch: 输入张量
            batch_idx: 批次索引

        Returns:
            预测结果
        """
        if isinstance(batch, tuple):
            x = batch[0]
        else:
            x = batch

        return self(x)

    def configure_optimizers(self) -> Dict[str, Any]:
        """
        配置优化器和学习率调度器

        Returns:
            包含优化器和学习率调度器的字典
        """
        optimizer = torch.optim.Adam(
            self.parameters(), lr=self.learning_rate, weight_decay=self.weight_decay
        )

        scheduler = {
            "scheduler": ReduceLROnPlateau(
                optimizer,
                mode="min",
                factor=0.5,
                patience=5,
                min_lr=1e-6,
                verbose="true",
            ),
            "monitor": "val_loss",
            "interval": "epoch",
            "frequency": 1,
        }

        return {"optimizer": optimizer, "lr_scheduler": scheduler}


# 多步预测版本的注意力增强LSTM模型
class LitLSTMWithAttentionMultiStep(LitLSTMWithAttention):
    """
    多步预测版本的注意力增强PyTorch Lightning LSTM模型
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        num_layers: int = 2,
        output_size: int = 1,
        horizon: int = 1,
        dropout: float = 0.1,
        bidirectional: bool = False,
        attention_type: str = "self",
        num_attention_heads: int = 8,
        use_attention_residual: bool = True,
        learning_rate: float = 1e-3,
        weight_decay: float = 1e-5,
    ):
        """
        初始化多步预测版本的注意力增强Lightning LSTM模型

        Args:
            input_size: 输入特征的维度
            hidden_size: LSTM隐藏层的维度，默认为128
            num_layers: LSTM的层数，默认为2
            output_size: 每个时间步的输出特征维度，默认为1
            horizon: 预测的时间步数，默认为1
            dropout: dropout比率，默认为0.1
            bidirectional: 是否使用双向LSTM，默认为False
            attention_type: 注意力类型，"self"或"multi_head"，默认为"self"
            num_attention_heads: 多头注意力的头数，默认为8
            use_attention_residual: 是否使用残差连接，默认为True
            learning_rate: 学习率，默认为1e-3
            weight_decay: 权重衰减系数，默认为1e-5
        """
        # 调用父类初始化
        super().__init__(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=output_size * horizon,  # 总输出大小是每个时间步的特征数乘以时间步数
            dropout=dropout,
            bidirectional=bidirectional,
            attention_type=attention_type,
            num_attention_heads=num_attention_heads,
            use_attention_residual=use_attention_residual,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
        )

        # 保存预测的时间步数
        self.horizon = horizon
        self.output_dim = output_size

    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播，并将输出重塑为多步预测格式

        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_size]
            mask: 可选的掩码张量

        Returns:
            输出张量，形状为 [batch_size, horizon, output_dim]
        """
        # 获取模型原始输出
        output = super().forward(x, mask)

        # 重塑为多步预测格式 [batch_size, horizon, output_dim]
        batch_size = x.size(0)
        output = output.view(batch_size, self.horizon, self.output_dim)

        return output

    def training_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        训练步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含损失的字典
        """
        x, y = batch
        y_hat = self(x)

        # 计算多步预测的MSE
        loss = self.criterion(y_hat, y)
        self.log("train_loss", loss, prog_bar=True, on_step=True, on_epoch=True)

        return {"loss": loss}

    def validation_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        验证步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含验证损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        val_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("val_loss", val_loss, prog_bar=True)
        self.log("val_mae", mae, prog_bar=True)
        self.log("val_rmse", rmse, prog_bar=True)

        return {"val_loss": val_loss, "val_mae": mae, "val_rmse": rmse}

    def test_step(
        self, batch: Tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        测试步骤

        Args:
            batch: 包含输入和目标的元组
            batch_idx: 批次索引

        Returns:
            包含测试损失和指标的字典
        """
        x, y = batch
        y_hat = self(x)

        test_loss = self.criterion(y_hat, y)

        # 计算MAE和RMSE
        mae = F.l1_loss(y_hat, y)
        rmse = torch.sqrt(self.criterion(y_hat, y))

        # 记录指标
        self.log("test_loss", test_loss)
        self.log("test_mae", mae)
        self.log("test_rmse", rmse)

        return {"test_loss": test_loss, "test_mae": mae, "test_rmse": rmse}
