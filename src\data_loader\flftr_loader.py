import json
import os
import shutil
import lightning as L
import pandas as pd
import logging

from torch.utils.data import DataLoader
from typing import Any, Optional
from datetime import datetime
from utils.data_utils import (
    fill_na_with_moving_average,
    downsample,
    split_series_data,
    split_data,
    normalize_dfs,
    normalize_dfs_by_params,
)
from dataset.time_series_dataset import (
    TimeSeriesDatasetAutoregressive,
    TimeSeriesDataset,
)
from enums import FileNameEnum as fne
from config import TrainConfig

# 原始数据列排序
_COLUMNS = [
    "TIME_STAMP",
    "SPEED_IN_FURNACE",
    "THICKNESS",
    "WIDTH",
    "RTF_TUBE_TEMP1",
    "RTF_TUBE_TEMP2",
    "RTF_TUBE_TEMP3",
    "RTF_TUBE_TEMP4",
    "RTF_TUBE_TEMP5",
    "RTF_TUBE_TEMP6",
    "RTF_TUBE_TEMP7",
    "RTF_TUBE_TEMP8",
    "RTF_TUBE_TEMP9",
    "RTF_TUBE_TEMP10",
    "RTF_TUBE_TEMP11",
    "RTF_POWER_TEMP1",
    "RTF_POWER_TEMP2",
    "RTF_POWER_TEMP3",
    "RTF_POWER_TEMP4",
    "RTF_POWER_TEMP5",
    "RTF_POWER_TEMP6",
    "RTF_POWER_TEMP7",
    "RTF_POWER_TEMP8",
    "RTF_POWER_TEMP9",
    "RTF_POWER_TEMP10",
    "RTF_POWER_TEMP11",
    "RTF_EXIT_TEMP",
    "SF_ZONE_TEMP",
]
# 索引列
_INDEX_COLUMN = "TIME_STAMP"
# 输入列
_INPUT_COLUMNS = [
    "SPEED_IN_FURNACE",
    "THICKNESS",
    "WIDTH",
    "RTF_POWER_TEMP1",
    "RTF_POWER_TEMP2",
    "RTF_POWER_TEMP3",
    "RTF_POWER_TEMP4",
    "RTF_POWER_TEMP5",
    "RTF_POWER_TEMP6",
    "RTF_POWER_TEMP7",
    "RTF_POWER_TEMP8",
    "RTF_POWER_TEMP9",
    "RTF_POWER_TEMP10",
    "RTF_POWER_TEMP11",
]
# 目标列
_TARGET_COLUMNS = ["RTF_EXIT_TEMP", "SF_ZONE_TEMP"]
# 异常列
_ANOMALY_COLUMNS = ["SPEED_IN_FURNACE"]
# 异常阈值
_ANOMALY_THRESHOLD_UPPER = [200]
_ANOMALY_THRESHOLD_LOWER = [20]

# 状态估计(State Estimation)模型输入列
_STATE_ESTIMATION_INPUT_COLUMNS = [
    "SPEED_IN_FURNACE",
    "THICKNESS",
    "WIDTH",
    "RTF_POWER_TEMP1",
    "RTF_POWER_TEMP2",
    "RTF_POWER_TEMP3",
    "RTF_POWER_TEMP4",
    "RTF_POWER_TEMP5",
    "RTF_POWER_TEMP6",
    "RTF_POWER_TEMP7",
    "RTF_POWER_TEMP8",
    "RTF_POWER_TEMP9",
    "RTF_POWER_TEMP10",
    "RTF_POWER_TEMP11",
]

_STATE_ESTIMATION_TARGET_COLUMNS = [
    "RTF_TUBE_TEMP1",
    "RTF_TUBE_TEMP2",
    "RTF_TUBE_TEMP3",
    "RTF_TUBE_TEMP4",
    "RTF_TUBE_TEMP5",
    "RTF_TUBE_TEMP6",
    "RTF_TUBE_TEMP7",
    "RTF_TUBE_TEMP8",
    "RTF_TUBE_TEMP9",
    "RTF_TUBE_TEMP10",
    "RTF_TUBE_TEMP11",
]


# 配置文件不存在时的默认配置
_DEFAULT_DEBUG = False
_DEFAULT_FREQ = "1min"
_DEFUALT_MIN_DATA_LENGTH = 512
_DEFAULT_SPLIT_RATIO = [0.8, 0.1, 0.1]
_DEFAULT_NORMALIZE_METHOD = "min-max"
_DEFAULT_SEQ_LENGTH = 256
_DEFAULT_LOOKBACK = 255
_DEFAULT_HORIZON = 1
_DEFAULT_BATCH_SIZE = 512


class FlftrLoader(L.LightningDataModule):
    def __init__(
        self,
        start_time: datetime,
        end_time: datetime,
        work_dir: str,
        config: TrainConfig,
    ) -> None:
        """
        Args:
            start_time (datetime): 开始时间
            end_time (datetime): 结束时间
            dir (str): 数据文件夹路径
        """
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.start_time = start_time
        self.end_time = end_time
        self.work_dir = work_dir
        self.meta_dir = os.path.join(self.work_dir, "meta")
        self.data_path = os.path.join(self.work_dir, f"{fne.SOURCE.value}.csv")

        # config
        self.debug = config.debug
        self.freq = config.freq
        self.min_data_length = config.min_data_length
        self.split_ratio = config.split_ratio
        self.normalize_method = config.normalize_method
        self.seq_length = config.input_len
        self.lookback = config.input_len
        self.horizon = config.output_len
        self.batch_size = config.batch_size
        # 数据集元信息
        self.normalize_meta: dict = {}

        # 数据集
        self.train_list: list[pd.DataFrame] = []
        self.valid_list: list[pd.DataFrame] = []
        self.test_list: list[pd.DataFrame] = []

        # 创建数据文件夹
        os.makedirs(self.work_dir, exist_ok=True)
        os.makedirs(self.meta_dir, exist_ok=True)

    def prepare_data(self) -> None:
        """
        准备数据, 模拟从数据库读取数据后存储至本地
        TODO: 从数据库读取数据
        """
        # 模拟从数据库读取数据后存储至本地
        source = "data/source/FLFTR10.csv"
        shutil.copy2(source, self.data_path)

    def setup(self, stage: Optional[str] = None) -> None:

        try:
            # 读取数据
            data = pd.read_csv(self.data_path)
            # 预处理
            data = self._process_data(data)
            # 分割数据
            train_dfs, valid_dfs, test_dfs = self._split_data(data)
            # 归一化数据
            self._normalize_data(train_dfs, valid_dfs, test_dfs)

        except Exception as e:
            raise e

    def _process_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        处理数据
        """
        self.logger.info("开始数据预处理...")

        # 1. 行和列排序
        data = data[_COLUMNS]
        data = data.sort_values(by=_INDEX_COLUMN, ascending=True)
        self.logger.info("排序完成...")

        if self.debug:
            self._save_data(data, f"{fne.SORTED.value}.csv")

        # 2. 时间戳转换为索引
        data.loc[:, _INDEX_COLUMN] = pd.to_datetime(data[_INDEX_COLUMN])
        data = data.set_index(_INDEX_COLUMN).infer_objects()
        self.logger.info("重建索引完成...")

        if self.debug:
            self._save_data(data, f"{fne.INDEXED.value}.csv")

        # 3. 异常数据处理
        for column in _ANOMALY_COLUMNS:
            # 删除阈值范围外的行
            data = data[data[column] > _ANOMALY_THRESHOLD_LOWER[0]]
            data = data[data[column] < _ANOMALY_THRESHOLD_UPPER[0]]

        if self.debug:
            self._save_data(data, f"{fne.ANOMALY_REMOVED.value}.csv")
        self.logger.info("异常值处理完成...")

        # 4. 缺失值处理：先使用移动平均填充NAN
        data = fill_na_with_moving_average(data)

        if self.debug:
            self._save_data(data, f"{fne.FILLED.value}.csv")
        self.logger.info("缺失值处理完成...")

        # 5. 重采样/时间戳对齐
        data = downsample(data, freq=self.freq)

        if self.debug:
            self._save_data(data, f"{fne.RESAMPLED.value}.csv")
        self.logger.info("重采样完成...")

        # 6. 删除依旧为NAN的行
        data = data.dropna()

        if self.debug:
            self._save_data(data, f"{fne.FINAL.value}.csv")

        self.logger.info("数据预处理完成...")
        return data

    def _split_data(
        self,
        data: pd.DataFrame,
    ) -> tuple[list[pd.DataFrame], list[pd.DataFrame], list[pd.DataFrame]]:
        """
        分割数据
        """
        self.logger.info("开始数据分割...")

        if len(self.split_ratio) != 3:
            raise ValueError("split_ratio长度必须等于3")

        split_ratio_tuple = (
            self.split_ratio[0],
            self.split_ratio[1],
            self.split_ratio[2],
        )

        # 1.根据时间索引空缺分割时序数据
        data_segments = split_series_data(
            data, time_gap=self.freq, min_length=self.min_data_length
        )

        if self.debug:
            for idx, segment in enumerate(data_segments):
                self._save_data(segment, f"{fne.SEGMENT.value}_{idx}.csv")
        self.logger.info("时序数据按时间分割完成...")

        # 2.根据数据集比例，返回数据集
        train_list, valid_list, test_list = split_data(
            data_segments, split_ratio=split_ratio_tuple
        )

        if self.debug:
            for idx, segment in enumerate(train_list):
                self._save_data(segment, f"{fne.TRAIN.value}_{idx}.csv")
            for idx, segment in enumerate(valid_list):
                self._save_data(segment, f"{fne.VALID.value}_{idx}.csv")
            for idx, segment in enumerate(test_list):
                self._save_data(segment, f"{fne.TEST.value}_{idx}.csv")
        self.logger.info("训练数据集分割完成...")

        return train_list, valid_list, test_list

    def _normalize_data(
        self,
        train_list: list[pd.DataFrame],
        valid_list: list[pd.DataFrame],
        test_list: list[pd.DataFrame],
    ) -> None:
        """
        归一化数据
        """
        self.logger.info("开始数据归一化...")

        self.train_list, self.normalize_meta = normalize_dfs(
            train_list, method=self.normalize_method
        )
        self.valid_list = normalize_dfs_by_params(valid_list, self.normalize_meta)
        self.test_list = normalize_dfs_by_params(test_list, self.normalize_meta)

        # 保存归一化元信息
        with open(
            os.path.join(self.meta_dir, f"{fne.NORMALIZE_META.value}.json"), "w"
        ) as f:
            json.dump(self.normalize_meta, f)

        self.logger.info("数据归一化完成...")

    def _save_data(self, data: pd.DataFrame, file_name: str) -> None:
        """
        保存数据
        """
        data.to_csv(os.path.join(self.work_dir, file_name), index=True)

    def train_dataloader(self) -> DataLoader:
        """获取训练集DataLoader"""
        dataset = TimeSeriesDataset(
            self.train_list,
            _INPUT_COLUMNS,
            _TARGET_COLUMNS,
            input_len=self.seq_length,
            output_len=1,
        )

        return DataLoader(dataset, batch_size=self.batch_size, shuffle=False)

    def val_dataloader(self) -> DataLoader:
        """获取验证集DataLoader"""
        dataset = TimeSeriesDataset(
            self.valid_list,
            _INPUT_COLUMNS,
            _TARGET_COLUMNS,
            input_len=self.seq_length,
            output_len=1,
        )

        return DataLoader(dataset, batch_size=self.batch_size, shuffle=False)

    def test_dataloader(self) -> DataLoader:
        """获取测试集DataLoader"""
        dataset = TimeSeriesDataset(
            self.test_list,
            _INPUT_COLUMNS,
            _TARGET_COLUMNS,
            input_len=self.seq_length,
            output_len=1,
        )

        return DataLoader(dataset, batch_size=self.batch_size, shuffle=False)

    def train_dataloader_se(self) -> DataLoader:
        """获取状态估计(State Estimation)模型训练数据集"""
        dataset = TimeSeriesDataset(
            self.train_list,
            _STATE_ESTIMATION_INPUT_COLUMNS,
            _STATE_ESTIMATION_TARGET_COLUMNS,
            input_len=self.seq_length,
            output_len=1,
        )

        return DataLoader(dataset, batch_size=self.batch_size, shuffle=False)

    def val_dataloader_se(self) -> DataLoader:
        """获取状态估计(State Estimation)模型验证数据集"""
        dataset = TimeSeriesDataset(
            self.valid_list,
            _STATE_ESTIMATION_INPUT_COLUMNS,
            _STATE_ESTIMATION_TARGET_COLUMNS,
            input_len=self.seq_length,
            output_len=1,
        )

        return DataLoader(dataset, batch_size=self.batch_size, shuffle=False)

    def test_dataloader_se(self) -> DataLoader:
        """获取状态估计(State Estimation)模型测试数据集"""
        dataset = TimeSeriesDataset(
            self.test_list,
            _STATE_ESTIMATION_INPUT_COLUMNS,
            _STATE_ESTIMATION_TARGET_COLUMNS,
            input_len=self.seq_length,
            output_len=1,
        )

        return DataLoader(dataset, batch_size=self.batch_size, shuffle=False)
