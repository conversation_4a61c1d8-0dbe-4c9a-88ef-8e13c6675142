import json
import logging
import glob
import pandas as pd
import numpy as np
import torch
from pathlib import Path
from typing import Dict, List, Tuple
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from config import Configs, Logger
from model.lstm import LitLSTM, LitLSTMMultiStep, LitLSTMWithAttention, LitLSTMWithAttentionMultiStep
from utils.data_utils import normalize_by_params, denormalize


class ModelPredictor:
    """模型预测器类，用于加载模型并进行预测"""

    def __init__(self, model_dir: str = "./data/test"):
        """
        初始化预测器

        Args:
            model_dir: 模型和元数据所在目录
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.model_dir = Path(model_dir)
        self.meta_dir = self.model_dir / "meta"
        self.data_dir = self.model_dir / "data"

        # 模型相关
        self.model = None
        self.normalize_meta = None

        # 数据列定义
        self.input_columns = [
            "STRIP_WIDTH", "STRIP_THICK", "SPEED", "HEAT_LOAD_NOF1", "HEAT_LOAD_NOF2",
            "HEAT_LOAD_NOF3", "HEAT_LOAD_NOF4", "HEAT_LOAD_NOF5", "HEAT_LOAD_RTF1",
            "HEAT_LOAD_RTF2", "HEAT_LOAD_SF", "TEMP_PH", "TEMP_NOF1", "TEMP_NOF2",
            "TEMP_NOF3", "TEMP_NOF4", "TEMP_NOF5", "TEMP_RTF1", "TEMP_RTF2", "TEMP_SF",
            "STRIP_TEMP_NOF", "STRIP_TEMP_RTF", "STRIP_TEMP_SF"
        ]

        self.target_columns = [
            "TEMP_PH", "TEMP_NOF1", "TEMP_NOF2", "TEMP_NOF3", "TEMP_NOF4", "TEMP_NOF5",
            "TEMP_RTF1", "TEMP_RTF2", "TEMP_SF", "STRIP_TEMP_NOF", "STRIP_TEMP_RTF", "STRIP_TEMP_SF"
        ]

        # 序列长度配置
        self.seq_length = 256

    def load_model_and_metadata(self) -> None:
        """加载模型和元数据"""
        try:
            # 加载归一化元数据
            meta_file = self.meta_dir / "normalize_meta.json"
            if not meta_file.exists():
                raise FileNotFoundError(f"元数据文件不存在: {meta_file}")

            with open(meta_file, 'r', encoding='utf-8') as f:
                self.normalize_meta = json.load(f)
            self.logger.info(f"成功加载归一化元数据，包含 {len(self.normalize_meta)} 个特征")

            # 加载模型
            model_file = self.model_dir / "lstm_model.ckpt"
            if not model_file.exists():
                raise FileNotFoundError(f"模型文件不存在: {model_file}")

            # 根据输入输出维度确定模型类型
            input_size = len(self.input_columns)
            output_size = len(self.target_columns)

            # 尝试加载不同类型的模型
            model_classes = [LitLSTM, LitLSTMWithAttention, LitLSTMMultiStep, LitLSTMWithAttentionMultiStep]

            for model_class in model_classes:
                try:
                    self.model = model_class.load_from_checkpoint(
                        str(model_file),
                        input_size=input_size,
                        output_size=output_size
                    )
                    self.model.eval()
                    self.logger.info(f"成功加载模型: {model_class.__name__}")
                    break
                except Exception as e:
                    self.logger.debug(f"尝试加载 {model_class.__name__} 失败: {e}")
                    continue

            if self.model is None:
                raise RuntimeError("无法加载模型，请检查模型文件格式")

        except Exception as e:
            self.logger.error(f"加载模型和元数据失败: {e}")
            raise

    def load_test_data(self) -> List[pd.DataFrame]:
        """加载所有测试数据文件"""
        try:
            test_files = sorted(glob.glob(str(self.data_dir / "data_test_*.csv")))
            if not test_files:
                raise FileNotFoundError(f"在 {self.data_dir} 中未找到测试数据文件")

            test_dataframes = []
            for file_path in test_files:
                df = pd.read_csv(file_path)

                # 检查必要的列是否存在
                missing_cols = [col for col in self.input_columns if col not in df.columns]
                if missing_cols:
                    self.logger.warning(f"文件 {file_path} 缺少列: {missing_cols}")
                    continue

                # 设置时间索引
                if 'INDEX' in df.columns:
                    df['INDEX'] = pd.to_datetime(df['INDEX'])
                    df = df.set_index('INDEX')

                # 只保留需要的列
                available_cols = [col for col in self.input_columns if col in df.columns]
                df = df[available_cols]

                # 删除缺失值
                df = df.dropna()

                if len(df) >= self.seq_length + 1:  # 至少需要seq_length+1行数据才能进行预测
                    test_dataframes.append(df)
                    self.logger.info(f"加载测试文件: {Path(file_path).name}, 数据形状: {df.shape}")
                else:
                    self.logger.warning(f"文件 {file_path} 数据量不足，跳过")

            if not test_dataframes:
                raise ValueError("没有有效的测试数据文件")

            return test_dataframes

        except Exception as e:
            self.logger.error(f"加载测试数据失败: {e}")
            raise

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """对数据进行预处理（归一化）"""
        try:
            # 检查元数据是否已加载
            if self.normalize_meta is None:
                raise ValueError("归一化元数据未加载")

            # 应用归一化
            df_normalized = normalize_by_params(df, self.normalize_meta)
            return df_normalized
        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            raise

    def create_sequences(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """创建时序数据序列"""
        try:
            sequences = []
            targets = []

            for i in range(len(data) - self.seq_length):
                # 输入序列：过去256行的所有特征
                seq = data.iloc[i:i + self.seq_length][self.input_columns].values
                # 目标：第257行的目标列
                target = data.iloc[i + self.seq_length][self.target_columns].values

                sequences.append(seq)
                targets.append(target)

            return np.array(sequences), np.array(targets)

        except Exception as e:
            self.logger.error(f"创建序列数据失败: {e}")
            raise

    def predict_single_file(self, df: pd.DataFrame) -> Dict:
        """对单个数据文件进行预测"""
        try:
            # 检查模型是否已加载
            if self.model is None:
                raise ValueError("模型未加载")

            # 预处理数据
            df_normalized = self.preprocess_data(df)

            # 创建序列
            X, y_true = self.create_sequences(df_normalized)

            if len(X) == 0:
                self.logger.warning("数据量不足，无法创建有效序列")
                return {}

            # 转换为张量
            X_tensor = torch.FloatTensor(X)

            # 模型预测
            with torch.no_grad():
                y_pred = self.model(X_tensor).numpy()

            # 反归一化预测结果和真实值
            y_pred_denorm = self._denormalize_predictions(y_pred)
            y_true_denorm = self._denormalize_predictions(y_true)

            # 计算评估指标
            metrics = self._calculate_metrics(y_true_denorm, y_pred_denorm)

            return {
                'predictions': y_pred_denorm,
                'actual': y_true_denorm,
                'metrics': metrics,
                'num_samples': len(X)
            }

        except Exception as e:
            self.logger.error(f"单文件预测失败: {e}")
            raise

    def _denormalize_predictions(self, predictions: np.ndarray) -> np.ndarray:
        """反归一化预测结果"""
        try:
            # 检查元数据是否已加载
            if self.normalize_meta is None:
                self.logger.warning("归一化元数据未加载，返回原始预测结果")
                return predictions

            # 创建临时DataFrame进行反归一化
            pred_df = pd.DataFrame(predictions, columns=self.target_columns)

            # 只对目标列进行反归一化
            target_meta = {col: self.normalize_meta[col] for col in self.target_columns
                          if col in self.normalize_meta}

            if not target_meta:
                self.logger.warning("没有找到目标列的归一化参数，返回原始预测结果")
                return predictions

            denorm_df = denormalize(pred_df, target_meta)
            return denorm_df.values

        except Exception as e:
            self.logger.error(f"反归一化失败: {e}")
            return predictions

    def _calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict:
        """计算评估指标"""
        try:
            metrics = {}

            # 整体指标
            mse = mean_squared_error(y_true, y_pred)
            mae = mean_absolute_error(y_true, y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_true, y_pred)

            metrics['overall'] = {
                'MSE': float(mse),
                'MAE': float(mae),
                'RMSE': float(rmse),
                'R2': float(r2)
            }

            # 每个目标列的指标
            metrics['per_column'] = {}
            for i, col in enumerate(self.target_columns):
                col_mse = mean_squared_error(y_true[:, i], y_pred[:, i])
                col_mae = mean_absolute_error(y_true[:, i], y_pred[:, i])
                col_rmse = np.sqrt(col_mse)
                col_r2 = r2_score(y_true[:, i], y_pred[:, i])

                metrics['per_column'][col] = {
                    'MSE': float(col_mse),
                    'MAE': float(col_mae),
                    'RMSE': float(col_rmse),
                    'R2': float(col_r2)
                }

            return metrics

        except Exception as e:
            self.logger.error(f"计算指标失败: {e}")
            return {}


def main():
    """主函数：执行模型预测"""
    try:
        # 初始化配置和日志
        Configs.initialize()
        Logger.initialize()

        logger = logging.getLogger(__name__)
        logger.info("开始执行模型预测")

        # 创建预测器
        predictor = ModelPredictor()

        # 加载模型和元数据
        logger.info("加载模型和元数据...")
        predictor.load_model_and_metadata()

        # 加载测试数据
        logger.info("加载测试数据...")
        test_dataframes = predictor.load_test_data()

        # 对每个测试文件进行预测
        all_results = []
        overall_metrics = {'predictions': [], 'actual': []}

        for i, df in enumerate(test_dataframes):
            logger.info(f"处理测试文件 {i+1}/{len(test_dataframes)}")

            result = predictor.predict_single_file(df)
            if result:
                all_results.append(result)
                overall_metrics['predictions'].append(result['predictions'])
                overall_metrics['actual'].append(result['actual'])

                # 记录单文件结果
                logger.info(f"文件 {i+1} 预测完成:")
                logger.info(f"  样本数量: {result['num_samples']}")
                logger.info(f"  整体RMSE: {result['metrics']['overall']['RMSE']:.4f}")
                logger.info(f"  整体MAE: {result['metrics']['overall']['MAE']:.4f}")
                logger.info(f"  整体R2: {result['metrics']['overall']['R2']:.4f}")

                # 记录每列的详细指标
                for col, metrics in result['metrics']['per_column'].items():
                    logger.info(f"  {col} - RMSE: {metrics['RMSE']:.4f}, MAE: {metrics['MAE']:.4f}, R2: {metrics['R2']:.4f}")

        # 计算总体指标
        if overall_metrics['predictions']:
            all_predictions = np.vstack(overall_metrics['predictions'])
            all_actual = np.vstack(overall_metrics['actual'])

            final_metrics = predictor._calculate_metrics(all_actual, all_predictions)

            logger.info("=" * 50)
            logger.info("总体预测结果:")
            logger.info(f"总样本数: {len(all_predictions)}")
            logger.info(f"整体RMSE: {final_metrics['overall']['RMSE']:.4f}")
            logger.info(f"整体MAE: {final_metrics['overall']['MAE']:.4f}")
            logger.info(f"整体R2: {final_metrics['overall']['R2']:.4f}")

            logger.info("\n各列详细指标:")
            for col, metrics in final_metrics['per_column'].items():
                logger.info(f"{col:15} - RMSE: {metrics['RMSE']:8.4f}, MAE: {metrics['MAE']:8.4f}, R2: {metrics['R2']:8.4f}")

            logger.info("模型预测完成！")
        else:
            logger.warning("没有成功的预测结果")

    except Exception as e:
        logger.error(f"预测过程发生错误: {e}")
        raise


if __name__ == "__main__":
    main()