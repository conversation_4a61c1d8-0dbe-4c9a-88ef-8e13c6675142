#!/usr/bin/env python3
"""
测试注意力增强LSTM模型的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import torch
import numpy as np
from src.model.lstm import (
    LitLSTMWithAttention,
    LitLSTMWithAttentionMultiStep,
    MultiHeadAttention,
    SelfAttention,
    LSTMWithAttentionModel,
)


def test_attention_layers():
    """测试注意力层的基本功能"""
    print("=== 测试注意力层 ===")
    
    batch_size, seq_len, d_model = 4, 10, 64
    x = torch.randn(batch_size, seq_len, d_model)
    
    # 测试自注意力
    print("测试自注意力层...")
    self_attention = SelfAttention(d_model=d_model, dropout=0.1)
    self_output = self_attention(x)
    print(f"输入形状: {x.shape}")
    print(f"自注意力输出形状: {self_output.shape}")
    assert self_output.shape == x.shape, "自注意力输出形状不匹配"
    
    # 测试多头注意力
    print("测试多头注意力层...")
    multi_head_attention = MultiHeadAttention(d_model=d_model, num_heads=8, dropout=0.1)
    multi_output = multi_head_attention(x, x, x)
    print(f"多头注意力输出形状: {multi_output.shape}")
    assert multi_output.shape == x.shape, "多头注意力输出形状不匹配"
    
    print("注意力层测试通过！\n")


def test_attention_lstm_model():
    """测试注意力增强LSTM基础模型"""
    print("=== 测试注意力增强LSTM基础模型 ===")
    
    batch_size, seq_len, input_size = 4, 20, 10
    hidden_size, num_layers, output_size = 32, 2, 1
    
    x = torch.randn(batch_size, seq_len, input_size)
    
    # 测试自注意力版本
    print("测试自注意力LSTM模型...")
    model_self = LSTMWithAttentionModel(
        input_size=input_size,
        hidden_size=hidden_size,
        num_layers=num_layers,
        output_size=output_size,
        attention_type="self",
        dropout=0.1,
    )
    output_self = model_self(x)
    print(f"输入形状: {x.shape}")
    print(f"自注意力LSTM输出形状: {output_self.shape}")
    assert output_self.shape == (batch_size, output_size), "自注意力LSTM输出形状不匹配"
    
    # 测试多头注意力版本
    print("测试多头注意力LSTM模型...")
    model_multi = LSTMWithAttentionModel(
        input_size=input_size,
        hidden_size=hidden_size,
        num_layers=num_layers,
        output_size=output_size,
        attention_type="multi_head",
        num_attention_heads=4,
        dropout=0.1,
    )
    output_multi = model_multi(x)
    print(f"多头注意力LSTM输出形状: {output_multi.shape}")
    assert output_multi.shape == (batch_size, output_size), "多头注意力LSTM输出形状不匹配"
    
    print("注意力增强LSTM基础模型测试通过！\n")


def test_lightning_attention_models():
    """测试PyTorch Lightning包装的注意力模型"""
    print("=== 测试Lightning注意力模型 ===")
    
    batch_size, seq_len, input_size = 4, 20, 10
    hidden_size, num_layers, output_size = 32, 2, 1
    horizon = 5
    
    x = torch.randn(batch_size, seq_len, input_size)
    y_single = torch.randn(batch_size, output_size)
    y_multi = torch.randn(batch_size, horizon, output_size)
    
    # 测试单步预测模型
    print("测试单步预测注意力模型...")
    lit_model_single = LitLSTMWithAttention(
        input_size=input_size,
        hidden_size=hidden_size,
        num_layers=num_layers,
        output_size=output_size,
        attention_type="self",
        dropout=0.1,
    )
    
    # 前向传播
    output_single = lit_model_single(x)
    print(f"单步预测输出形状: {output_single.shape}")
    assert output_single.shape == (batch_size, output_size), "单步预测输出形状不匹配"
    
    # 训练步骤
    train_loss = lit_model_single.training_step((x, y_single), 0)
    print(f"训练损失: {train_loss['loss'].item():.4f}")
    
    # 测试多步预测模型
    print("测试多步预测注意力模型...")
    lit_model_multi = LitLSTMWithAttentionMultiStep(
        input_size=input_size,
        hidden_size=hidden_size,
        num_layers=num_layers,
        output_size=output_size,
        horizon=horizon,
        attention_type="multi_head",
        num_attention_heads=4,
        dropout=0.1,
    )
    
    # 前向传播
    output_multi = lit_model_multi(x)
    print(f"多步预测输出形状: {output_multi.shape}")
    assert output_multi.shape == (batch_size, horizon, output_size), "多步预测输出形状不匹配"
    
    # 训练步骤
    train_loss_multi = lit_model_multi.training_step((x, y_multi), 0)
    print(f"多步预测训练损失: {train_loss_multi['loss'].item():.4f}")
    
    print("Lightning注意力模型测试通过！\n")


def test_model_parameters():
    """测试模型参数数量"""
    print("=== 测试模型参数数量 ===")
    
    input_size, hidden_size, num_layers, output_size = 10, 64, 2, 1
    
    # 原始LSTM模型（用于对比）
    from src.model.lstm import LitLSTM
    original_model = LitLSTM(
        input_size=input_size,
        hidden_size=hidden_size,
        num_layers=num_layers,
        output_size=output_size,
    )
    original_params = sum(p.numel() for p in original_model.parameters())
    
    # 注意力增强模型
    attention_model = LitLSTMWithAttention(
        input_size=input_size,
        hidden_size=hidden_size,
        num_layers=num_layers,
        output_size=output_size,
        attention_type="self",
    )
    attention_params = sum(p.numel() for p in attention_model.parameters())
    
    # 多头注意力模型
    multi_head_model = LitLSTMWithAttention(
        input_size=input_size,
        hidden_size=hidden_size,
        num_layers=num_layers,
        output_size=output_size,
        attention_type="multi_head",
        num_attention_heads=8,
    )
    multi_head_params = sum(p.numel() for p in multi_head_model.parameters())
    
    print(f"原始LSTM模型参数数量: {original_params:,}")
    print(f"自注意力LSTM模型参数数量: {attention_params:,}")
    print(f"多头注意力LSTM模型参数数量: {multi_head_params:,}")
    print(f"自注意力模型参数增加: {attention_params - original_params:,} ({(attention_params/original_params-1)*100:.1f}%)")
    print(f"多头注意力模型参数增加: {multi_head_params - original_params:,} ({(multi_head_params/original_params-1)*100:.1f}%)")
    
    print("模型参数测试完成！\n")


def main():
    """主测试函数"""
    print("开始测试注意力增强LSTM模型...\n")
    
    # 设置随机种子以确保结果可重现
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        test_attention_layers()
        test_attention_lstm_model()
        test_lightning_attention_models()
        test_model_parameters()
        
        print("🎉 所有测试通过！注意力增强LSTM模型实现正确。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
