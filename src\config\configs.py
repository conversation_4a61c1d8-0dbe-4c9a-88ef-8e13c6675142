import tomllib
from pathlib import Path
from dataclasses import dataclass, field

_CONFIG_FILE_PATH = "config/config.toml"


@dataclass
class TrainConfig:
    save_data: bool = True  # 是否保存训练数据数据
    debug: bool = True  # 是否开启调试模式
    freq: str = "1min"  # 数据采样频率
    min_data_length: int = 512  # 最小数据长度要求
    split_ratio: list[float] = field(
        default_factory=lambda: [0.8, 0.1, 0.1]
    )  # 训练集、验证集、测试集的划分比例
    normalize_method: str = "min-max"  # 数据归一化方法
    input_len: int = 256  # 输入序列长度(回看窗口)
    output_len: int = 1  # 预测序列长度(预测步长)

    hidden_size: int = 128  # 隐藏层大小
    num_layers: int = 2  # 网络层数
    dropout: float = 0.1  # Dropout比率，用于防止过拟合
    bidirectional: bool = False  # 是否使用双向网络
    learning_rate: float = 1e-3  # 学习率
    weight_decay: float = 1e-5  # 权重衰减，用于正则化
    batch_size: int = 512  # 批处理大小
    epochs: int = 100  # 训练轮数
    accelerator: str = "auto"  # 自动选择加速器（CPU/GPU）
    devices: str = "auto"  # 自动选择设备数量
    gradient_clip_val: float = 1.0  # 梯度裁剪值，防止梯度爆炸
    early_stopping_patience: int = 10  # 早停patience，连续多少轮无改善后停止训练

@dataclass
class AnnealingFurnaceConfig:
    total_length: float = 171.1  # 炉体总长, m
    ph_length: float = 23.08
    nof_length: float = 30.42
    rtf_length: float = 37.7
    sf_length: float = 35.48
    jcf_length: float = 44.42

    weld1_max: float = 1200  # 焊缝位置1（距焊机）最大值
    weld2_max: float = 518  # 焊缝位置2（距入炉密封辊）最大值


@dataclass
class InfluxDBCofing:
    host: str = "127.0.0.1"
    port: int = 8086
    username: str = "root"
    password: str = "123456"
    database: str = "annealing_furnace"


@dataclass
class LoggingConfig:
    log_dir: str = "logs"
    level: str = "INFO"
    format: str = "[%(asctime)s] - [%(name)s] - [%(levelname)s] - %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    file_handler: bool = True
    file_path: str = "logs/app.log"
    console_handler: bool = True
    max_days: int = 30


class Configs:

    _TrainConfig = None
    _AFConfig = None
    _LoggingConfig = None
    _InfluxDBCofing = None

    @classmethod
    def initialize(cls, config_path_str: str = _CONFIG_FILE_PATH):

        config_path = Path(config_path_str)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        try:
            with open(_CONFIG_FILE_PATH, "rb") as f:
                config = tomllib.load(f)
                cls._TrainConfig = TrainConfig(**config["train"])
                cls._AFConfig = AnnealingFurnaceConfig(**config["furnace"])
                cls._LoggingConfig = LoggingConfig(**config["logging"])
                cls._InfluxDBCofing = InfluxDBCofing(**config["influxdb"])
            
        except Exception as e:
            raise ValueError(f"加载配置文件时出错: {e}")
    
    @classmethod
    def get_train_config(cls) -> TrainConfig:
        return cls._TrainConfig or TrainConfig()
    
    @classmethod
    def get_af_config(cls) -> AnnealingFurnaceConfig:
        return cls._AFConfig or AnnealingFurnaceConfig()
    
    @classmethod
    def get_logging_config(cls) -> LoggingConfig:
        return cls._LoggingConfig or LoggingConfig()
    
    @classmethod
    def get_influx_db_config(cls) -> InfluxDBCofing:
        return cls._InfluxDBCofing or InfluxDBCofing()



